/*
 * Copyright (c) 2012-2025. Sencha Inc.
 */

package com.sencha.tools.compiler.ast.js;

import com.google.javascript.jscomp.parsing.parser.util.SourcePosition;

/**
 * A special AST node that preserves the original source code without transformation.
 * This is used for modern JavaScript features (ES7+) that should be passed through
 * to the Closure Compiler without modification by the AST converter.
 *
 * <p>This node is particularly useful for:</p>
 * <ul>
 *   <li>Optional chaining operators (?.)</li>
 *   <li>Nullish coalescing operators (??)</li>
 *   <li>Other ES6+ features that Closure Compiler can handle natively</li>
 * </ul>
 *
 * <p>When the source is regenerated, this node outputs its original source text
 * exactly as it was parsed, avoiding any AST transformation that might alter
 * the modern syntax.</p>
 *
 * @since 7.0.0
 */
public class PassthroughNode extends BaseNode {

    private String originalSyntax;
    private String treeType; // For debugging/logging purposes

    /**
     * Creates a new empty PassthroughNode.
     */
    public PassthroughNode() {
        this.originalSyntax = "";
        this.treeType = "unknown";
    }

    /**
     * Creates a new PassthroughNode with the original source text.
     *
     * @param originalSyntax The original source code to preserve
     */
    public PassthroughNode(String originalSyntax) {
        this.originalSyntax = originalSyntax != null ? originalSyntax : "";
        this.treeType = "unknown";
    }

    /**
     * Gets the original source code that this node preserves.
     *
     * @return The original source text
     */
    public String getOriginalSyntax() {
        return originalSyntax;
    }

    /**
     * Sets the original source code.
     *
     * @param originalSyntax The source text to preserve
     */
    public void setOriginalSyntax(String originalSyntax) {
        this.originalSyntax = originalSyntax != null ? originalSyntax : "";
    }

    /**
     * Gets the tree type description for debugging purposes.
     *
     * @return The tree type description
     */
    public String getTreeType() {
        return treeType;
    }

    /**
     * Sets the tree type description.
     *
     * @param treeType A description of what this node represents
     */
    public void setTreeType(String treeType) {
        this.treeType = treeType != null ? treeType : "unknown";
    }

    /**
     * Gets the length of the original source text.
     *
     * @return The length of the source, or 0 if source is null
     */
    public int getSourceLength() {
        return originalSyntax != null ? originalSyntax.length() : 0;
    }

    @Override
    public String toString() {
        return String.format("PassthroughNode[type=%s, source='%s']",
                           treeType,
                           originalSyntax != null ? originalSyntax : "null");
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;

        PassthroughNode that = (PassthroughNode) obj;

        if (originalSyntax != null ? !originalSyntax.equals(that.originalSyntax) : that.originalSyntax != null) {
            return false;
        }
        if (treeType != null ? !treeType.equals(that.treeType) : that.treeType != null) {
            return false;
        }

        return true;
    }

    @Override
    public int hashCode() {
        int result = originalSyntax != null ? originalSyntax.hashCode() : 0;
        result = 31 * result + (treeType != null ? treeType.hashCode() : 0);
        return result;
    }

    /**
     * Implementation of the abstract doVisit method from BaseNode.
     * This method is called when a visitor visits this node.
     *
     * @param vis The node visitor
     * @param <T> The visitor's context type
     */
    @Override
    <T> void doVisit(NodeVisitor<T> vis) {
        vis.onPassthroughNode(this);
    }

    /**
     * Implementation of the abstract descend method from BaseNode.
     * Since PassthroughNode preserves original source without transformation,
     * it has no child nodes to visit.
     *
     * @param vis The node visitor
     * @param <T> The visitor's context type
     */
    @Override
    public <T> void descend(NodeVisitor<T> vis) {
        // PassthroughNode has no child nodes to visit since it preserves
        // the original source text without AST transformation
    }
}
